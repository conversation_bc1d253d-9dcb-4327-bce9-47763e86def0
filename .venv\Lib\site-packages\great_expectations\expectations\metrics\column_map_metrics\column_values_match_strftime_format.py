from __future__ import annotations

from datetime import datetime

from great_expectations.compatibility import pyspark
from great_expectations.compatibility.pyspark import functions as F
from great_expectations.execution_engine import (
    PandasExecutionEngine,
    SparkDFExecutionEngine,
)
from great_expectations.expectations.metrics.map_metric_provider import (
    ColumnMapMetricProvider,
    column_condition_partial,
)


class ColumnValuesMatchStrftimeFormat(ColumnMapMetricProvider):
    condition_metric_name = "column_values.match_strftime_format"
    condition_value_keys = ("strftime_format",)

    @column_condition_partial(engine=PandasExecutionEngine)
    def _pandas(cls, column, strftime_format, **kwargs):
        def is_parseable_by_format(val):
            try:
                datetime.strptime(val, strftime_format)  # noqa: DTZ007 # FIXME CoP
                return True
            except TypeError:
                raise TypeError(  # noqa: TRY003 # FIXME CoP
                    "Values passed to expect_column_values_to_match_strftime_format must be of type string.\nIf you want to validate a column of dates or timestamps, please call the expectation before converting from string format."  # noqa: E501 # FIXME CoP
                )
            except ValueError:
                return False

        return column.map(is_parseable_by_format)

    @column_condition_partial(engine=SparkDFExecutionEngine)
    def _spark(cls, column, strftime_format, **kwargs):
        # Below is a simple validation that the provided format can both format and parse a datetime object.  # noqa: E501 # FIXME CoP
        # %D is an example of a format that can format but not parse, e.g.
        try:
            datetime.strptime(  # noqa: DTZ007 # FIXME CoP
                datetime.strftime(datetime.now(), strftime_format),  # noqa: DTZ005 # FIXME CoP
                strftime_format,
            )
        except ValueError as e:
            raise ValueError(f"Unable to use provided strftime_format: {e!s}")  # noqa: TRY003 # FIXME CoP

        def is_parseable_by_format(val):
            if val is None:
                return False
            try:
                datetime.strptime(val, strftime_format)  # noqa: DTZ007 # FIXME CoP
                return True
            except TypeError:
                raise TypeError(  # noqa: TRY003 # FIXME CoP
                    "Values passed to expect_column_values_to_match_strftime_format must be of type string.\nIf you want to validate a column of dates or timestamps, please call the expectation before converting from string format."  # noqa: E501 # FIXME CoP
                )
            except ValueError:
                return False

        success_udf = F.udf(is_parseable_by_format, pyspark.types.BooleanType())
        return success_udf(column)
