<div class="card d-md-block d-none" style="max-height: 75vh">
  <div class="card-header p-2">
    <strong>Table of Contents</strong>
  </div>
  <div class="card-body p-0" style="overflow: auto; height: 100%">
    <nav id="navigation" class="rounded navbar   bg-light ge-navigation-sidebar-container p-1" style="max-height: 65vh;">
      <ul class="nav nav-pills ge-navigation-sidebar-content col-12 p-0" style="max-height: 65vh">
        {% for section in sections %}
          {% if section['section_name'] == "Overview" %}
            <li class="nav-item col-12">
              <a class="nav-link ge-navigation-sidebar-link" href="#section-{{ loop.index }}"
               style="white-space: normal; word-break: break-all;overflow-wrap: normal;">
                <strong>{{ section['section_name'] }}</strong>
              </a>
            </li>
          {% else %}
            <li class="nav-item col-12">
              <a class="nav-link ge-navigation-sidebar-link ml-1" href="#section-{{ loop.index }}"
                 style="white-space: normal; word-break: break-all;overflow-wrap: normal;">
                {{ section['section_name'] }}
              </a>
            </li>
          {% endif %}
        {% endfor %}
      </ul>
    </nav>
  </div>
</div>
