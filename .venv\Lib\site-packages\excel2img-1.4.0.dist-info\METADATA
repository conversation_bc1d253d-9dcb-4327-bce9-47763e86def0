Metadata-Version: 2.4
Name: excel2img
Version: 1.4.0
Summary: Save ranges from Excel documents as images
Home-page: https://github.com/glexey/excel2img
Author: <PERSON><PERSON>
Author-email: <EMAIL>
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: Win32 (MS Windows)
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Topic :: Documentation
Classifier: Topic :: Multimedia :: Graphics :: Graphics Conversion
Classifier: Topic :: Office/Business :: Office Suites
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Utilities
License-File: LICENSE
License-File: AUTHORS
Requires-Dist: Pillow>=3.3.1
Requires-Dist: pypiwin32
Dynamic: author
Dynamic: author-email
Dynamic: description
Dynamic: home-page
Dynamic: license-file
Dynamic: requires-dist

Save ranges from Excel documents as images
