
# This file was generated by 'versioneer.py' (0.18) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-05-28T15:40:23+0000",
 "dirty": false,
 "error": null,
 "full-revisionid": "257f9d3e869380050d566cce8f7c239d1564cc3f",
 "version": "1.4.6"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
