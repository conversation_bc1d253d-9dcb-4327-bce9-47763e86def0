{% if expectation_suite_name %}
  {% set expectation_suite_name_dot_count = expectation_suite_name.count(".") -%}
{% endif %}

{% set edit_suite_command = "great_expectations suite edit " + expectation_suite_name  %}

{# TODO move this logic into a testable method on DefaultJinjaView #}
{% if "ValidationResults" in renderer_type or "ProfilingResults" in renderer_type %}
  {% set static_images_dir = ((expectation_suite_name_dot_count + 3) * "../") + "static/images/" -%}
{% elif "ExpectationSuite" in renderer_type %}
  {% set static_images_dir = ((expectation_suite_name_dot_count + 1) * "../") + "static/images/" -%}
{% elif "SiteIndex" in renderer_type %}
  {% set static_images_dir = "./static/images/" -%}
{% endif %}

<script type="text/javascript">
$(function() {
    $('button.copy-edit-command').click(function() {
        $('.edit-command').focus();
        $('.edit-command').select();
        document.execCommand('copy');
    });
});
</script>

<div class="modal fade ge-expectation-editing-instructions-modal" tabindex="-1" role="dialog" aria-labelledby="ge-expectation-editing-instructions-modal-title"
     aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ge-expectation-editing-instructions-modal-title">How to Edit This Expectation Suite</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" style="height: 350px">
        <p>Expectations are best <strong>edited interactively in Jupyter notebooks</strong>.</p>
        <p>To automatically generate a notebook that does this run:</p>
        <div class="input-group mb-3">
          {% if batch_kwargs %}
            <textarea class="form-control edit-command" readonly>{{ edit_suite_command | safe }}</textarea>
          {% else %}
            <input type="text" class="form-control edit-command" readonly value="{{ edit_suite_command | safe }}">
          {% endif %}
            <div class="input-group-append">
                <button class="btn btn-primary copy-edit-command" type="button"><i class="far fa-clipboard"></i> Copy</button>
            </div>
        </div>
      <p>Once you have made your changes and <strong>run the entire notebook</strong> you can kill the notebook by pressing <strong>Ctr-C</strong> in your terminal.</p>
      <p>Because these notebooks are generated from an Expectation Suite, these notebooks are <strong>entirely disposable</strong>.</p>
      </div>
    </div>
  </div>
</div>
