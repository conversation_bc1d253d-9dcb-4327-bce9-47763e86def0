{% include 'content_block_header.j2' %}

{% if "styling" in content_block and "body" in content_block["styling"] -%}
    {% set content_block_body_styling = content_block["styling"]["body"] | render_styling -%}
{% else -%}
    {% set content_block_body_styling = "" -%}
{% endif -%}

<ul id="{{content_block_id}}-body" {{ content_block_body_styling | replace("{{section_id}}", section_id) | replace("{{content_block_id}}", content_block_id) }}>
    {% for bullet_point in content_block["bullet_list"] -%}
        {% if bullet_point is mapping and "styling" in bullet_point %}
            {% set bullet_point_styling = bullet_point["styling"].get("parent", {}) | render_styling %}
        {% else %}
            {% set bullet_point_styling = "" %}
        {% endif %}
        <li {{ bullet_point_styling }}>{{ bullet_point | render_content_block }}</li>
    {% endfor %}
</ul>
