<script>
  function rowStyleLinks(row, index) {
    return {
      css: {
        cursor: "pointer"
      }
    }
  }

  function rowAttributesLinks(row, index) {
    return {
      "class": "clickable-row",
      "data-href": row._table_row_link_path
    }
  }

  function expectationSuiteNameFilterDataCollector(value, row, formattedValue) {
    return row._expectation_suite_name_sort;
  }

  function validationSuccessFilterDataCollector(value, row, formattedValue) {
    return row._validation_success_text;
  }

    function getFormattedDateWithoutTime  (d) {

        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

        if (month.length < 2)
            month = '0' + month;
        if (day.length < 2)
            day = '0' + day;

        return [year, month, day].join('-');
    }

  function formatRuntimeDateForFilter(text, value, field, data){
    const cellValueAsDateObj = new Date(value);
    return text == getFormattedDateWithoutTime(cellValueAsDateObj);
  }

  function clearTableFilters(tableId) {
    $(`#${tableId}`).bootstrapTable('clearFilterControl');
    $(`#${tableId}`).bootstrapTable('resetSearch');
  }
</script>
