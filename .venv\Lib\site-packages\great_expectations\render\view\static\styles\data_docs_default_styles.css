{% if "ValidationResults" in renderer_type or "ProfilingResults" in renderer_type %}
  {% set static_fonts_dir = "../../../../../static/fonts/" -%}
{% elif "ExpectationSuite" in renderer_type %}
  {% set static_fonts_dir = "../../../../static/fonts/" -%}
{% elif "SiteIndex" in renderer_type %}
  {% set static_fonts_dir = "./static/fonts/" -%}
{% endif %}

body {
  position: relative;
}

.container {
  padding-top: 50px;
}

.sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 90px;
  z-index: 1;
}

.ge-section {
  clear: both;
  margin-bottom: 30px;
  padding-bottom: 20px;
}

.popover {
  max-width: 100%;
}

.cooltip {
  display: inline-block;
  position: relative;
  text-align: left;
  cursor: pointer;
}

.cooltip .top {
  min-width: 200px;
  top: -6px;
  left: 50%;
  transform: translate(-50%, -100%);
  padding: 10px 20px;
  color: #FFFFFF;
  background-color: #222222;
  font-weight: normal;
  font-size: 13px;
  border-radius: 8px;
  position: absolute;
  z-index: 99999999 !important;
  box-sizing: border-box;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.5);
  display: none;
}

.cooltip:hover .top {
  display: block;
  z-index: 99999999 !important;
}

.cooltip .top i {
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -12px;
  width: 24px;
  height: 12px;
  overflow: hidden;
}

.cooltip .top i::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  background-color: #222222;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.5);
}

ul {
  padding-inline-start: 20px;
}

.show-scrollbars {
  overflow: auto;
}

td .show-scrollbars {
  max-height: 80vh;
}

/*.show-scrollbars ul {*/
/*  padding-bottom: 20px*/
/*}*/

.show-scrollbars::-webkit-scrollbar {
  -webkit-appearance: none;
}

.show-scrollbars::-webkit-scrollbar:vertical {
  width: 11px;
}

.show-scrollbars::-webkit-scrollbar:horizontal {
  height: 11px;
}

.show-scrollbars::-webkit-scrollbar-thumb {
  border-radius: 8px;
  border: 2px solid white; /* should match background, can't be transparent */
  background-color: rgba(0, 0, 0, .5);
}

#ge-cta-footer {
  opacity: 0.9;
  border-left-width: 4px
}

.carousel-caption {
    position: relative;
    left: 0;
    top: 0;
}

footer {
  position: fixed;
  border-top: 1px solid #98989861;
  bottom: 0;
  left: 0;
  right: 0;
  height: 32px;
  padding: 4px;
  font-size: 14px;
  text-align: right;
  width: 100%;
  background: white;
  z-index: 100000;
}
footer a {
  padding-right: 8px;
  color: #ff6210;
  font-weight: 600;
}

footer a:hover {
    color: #bc490d;
    text-decoration: underline;
}

/* some css overrides for dark mode*/
@media (prefers-color-scheme: dark) {
  .table {
    color: #f1f1f1 !important;
    background-color: #212529;
  }
  .table-bordered{
    border: #f1f1f1;
  }
  .table-hover tbody tr:hover {
    color: #f1f1f1;
    background-color: rgba(255,255,255,.075);
  }
  .form-control:disabled,
  .form-control[readonly]{
    background-color: #343a40;
    opacity: .8;
  }

  .bg-light {
    background: inherit !important;
  }

  .code-snippet {
    background: #CDCDCD !important;
  }

  .alert-secondary a {
    color: #0062cc;
  }

  .alert-secondary a:focus, .alert-secondary a:hover{
    color: #004fa5;
  }

  .navbar-brand a {
    background: url('https://great-expectations-web-assets.s3.us-east-2.amazonaws.com/full_logo_dark.png') 0 0 no-repeat;
    background-size: 228.75px 50px;
    display: inline-block
  }
  .navbar-brand a img {
    visibility:hidden
  }
  footer {
    border-top: 1px solid #ffffff61;
    background: black;
    z-index: 100000;
  }
  footer a {
    color: #ff6210;
  }

  footer a:hover {
    color: #ff6210;
  }
}
