#  holidays
#  --------
#  A fast, efficient Python library for generating country, province and state
#  specific sets of holidays on the fly. It aims to make determining whether a
#  specific date is a holiday as fast and flexible as possible.
#
#  Authors: <AUTHORS>
# <AUTHOR> <EMAIL> (c) 2017-2023
# <AUTHOR> <EMAIL> (c) 2014-2017
#  Website: https://github.com/vacanza/holidays
#  License: MIT (see LICENSE file)

# flake8: noqa: F401

from holidays.groups.balinese_saka import BalineseSakaCalendarHolidays
from holidays.groups.buddhist import BuddhistCalendarHolidays
from holidays.groups.chinese import ChineseCalendarHolidays
from holidays.groups.christian import ChristianHolidays
from holidays.groups.custom import StaticHolidays
from holidays.groups.eastern import EasternCalendarHolidays
from holidays.groups.hebrew import HebrewCalendarHolidays
from holidays.groups.hindu import HinduCalendarHolidays
from holidays.groups.international import InternationalHolidays
from holidays.groups.islamic import IslamicHolidays
from holidays.groups.persian import PersianCalendarHolidays
from holidays.groups.sinhala import SinhalaCalendarHolidays
from holidays.groups.thai import ThaiCalendarHolidays
