{% if not content_block_body_styling and content_block.get("styling") %}
    {% set content_block_body_styling = content_block.get("styling") | render_styling %}
{% endif %}

<div id="{{content_block_id}}-body" {{ content_block_body_styling | replace("{{section_id}}", section_id) | replace("{{content_block_id}}", content_block_id) }}>
  {% include 'content_block_header.j2' %}
  {% for text in content_block["text"] -%}
      {% if text is mapping and "styling" in text -%}
          {% set paragraph_styling = text["styling"].get("parent", {}) | render_styling -%}
      {% else -%}
          {% set paragraph_styling = "" -%}
      {% endif -%}

      {% if text is mapping and text.get("content_block_type") == "markdown" %}
        <div {{ paragraph_styling }}>{{ text | render_content_block }}</div>
      {% else %}
        <p {{ paragraph_styling }}>{{ text | render_content_block }}</p>
      {% endif %}
    {% endfor -%}
</div>
