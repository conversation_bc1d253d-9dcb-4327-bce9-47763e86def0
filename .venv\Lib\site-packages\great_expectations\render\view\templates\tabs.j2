{% include 'content_block_header.j2' %}

{% include 'content_block_header.j2' %}

{% if "styling" in content_block and "body" in content_block["styling"] -%}
    {% set content_block_body_styling = content_block["styling"]["body"] | render_styling -%}
{% else -%}
    {% set content_block_body_styling = "" -%}
{% endif -%}

{% set tabs_container_id = content_block_id ~ "-tabs-container" %}
{% set tabs_nav_id = content_block_id ~ "-tabs-nav" %}
{% set tabs_content_id = content_block_id ~ "-tabs-content" %}
{% set tabs = content_block["tabs"] %}

<div
        id="{{ tabs_container_id }}"
        {{ content_block_body_styling | replace("{{section_id}}", section_id) | replace("{{content_block_id}}", content_block_id) }}
>
  <ul class="nav nav-tabs" id={{ tabs_nav_id }} role="tablist">
    {% for tab in tabs %}
      <li class="nav-item">
        <a
            class="nav-link{{ ' active' if loop.index == 1 }}"
            id="{{ tab.get('tab_name', '').strip().replace(' ', "-") ~ "-tab" }}"
            data-toggle="tab"
            href="#{{ tab.get('tab_name', '').strip().replace(' ', "-") }}"
            role="tab"
            aria-selected="{{ 'true' if loop.index == 1 else 'false' }}"
            aria-controls="{{ tab.get('tab_name', '').strip().replace(' ', "-") }}">
          {{ tab.get('tab_name', '') }}
        </a>
      </li>
    {% endfor %}
  </ul>

  <div class="tab-content" id="{{ tabs_content_id }}">
    {% for tab in tabs %}
      <div
        class="tab-pane fade{{ ' show active' if loop.index == 1 }}"
        id="{{ tab.get('tab_name', '').strip().replace(' ', "-") }}"
        role="tabpanel"
        aria-labelledby="{{ tab.get('tab_name', '').strip().replace(' ', "-") ~ "-tab" }}">
        {{ tab.get("tab_content") | render_content_block(content_block_id=content_block_id ~ '-' ~ loop.index) }}
      </div>
    {% endfor %}
  </div>
</div>
