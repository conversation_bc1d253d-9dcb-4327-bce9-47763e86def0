{% include 'content_block_header.j2' %}

{%- if index -%}
    {%- set child_id = "-child-" ~ index -%}
{%- else -%}
    {%- set child_id = "" -%}
{%- endif -%}

<div class="show-scrollbars">
  <div id="{{content_block_id}}-graph{{child_id}}" {{content_block_body_styling | replace("{{section_id}}", section_id) | replace("{{content_block_id}}", content_block_id)}}></div>
</div>
<script>
    // Assign the specification to a local variable vlSpec.
    vlSpec = {{content_block["graph"]}};
    // Embed the visualization in the container with id `vis`
    vegaEmbed('#{{content_block_id}}-graph{{child_id}}', vlSpec, {
        actions: false
    }).then(result=>console.log(result)).catch(console.warn);
</script>
