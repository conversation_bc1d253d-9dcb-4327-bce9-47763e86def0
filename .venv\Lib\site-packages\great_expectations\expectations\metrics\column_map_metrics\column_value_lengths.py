from __future__ import annotations

from typing import TYPE_CHECKING, Optional

from great_expectations.compatibility.pyspark import functions as F
from great_expectations.compatibility.sqlalchemy import sqlalchemy as sa
from great_expectations.compatibility.typing_extensions import override
from great_expectations.core.metric_function_types import (
    MetricPartialFunctionTypeSuffixes,
)
from great_expectations.execution_engine import (
    ExecutionEngine,
    PandasExecutionEngine,
    SparkDFExecutionEngine,
    SqlAlchemyExecutionEngine,
)
from great_expectations.expectations.metrics.map_metric_provider import (
    ColumnMapMetricProvider,
    column_condition_partial,
    column_function_partial,
)
from great_expectations.util import pandas_series_between
from great_expectations.validator.metric_configuration import MetricConfiguration

if TYPE_CHECKING:
    from great_expectations.expectations.expectation_configuration import (
        ExpectationConfiguration,
    )


class ColumnValuesValueLengthEquals(ColumnMapMetricProvider):
    condition_metric_name = "column_values.value_length.equals"
    condition_value_keys = ("value",)

    @column_condition_partial(engine=PandasExecutionEngine)
    def _pandas(cls, column, value, _metrics, **kwargs):
        column_lengths, _, _ = _metrics.get(
            f"column_values.value_length.{MetricPartialFunctionTypeSuffixes.MAP.value}"
        )
        return column_lengths == value

    @column_condition_partial(engine=SqlAlchemyExecutionEngine)
    def _sqlalchemy(cls, column, value, _metrics, **kwargs):
        column_lengths, _, _ = _metrics.get(
            f"column_values.value_length.{MetricPartialFunctionTypeSuffixes.MAP.value}"
        )
        return column_lengths == value

    @column_condition_partial(engine=SparkDFExecutionEngine)
    def _spark(cls, column, value, _metrics, **kwargs):
        column_lengths, _, _ = _metrics.get(
            f"column_values.value_length.{MetricPartialFunctionTypeSuffixes.MAP.value}"
        )
        return column_lengths == value

    @classmethod
    @override
    def _get_evaluation_dependencies(
        cls,
        metric: MetricConfiguration,
        configuration: Optional[ExpectationConfiguration] = None,
        execution_engine: Optional[ExecutionEngine] = None,
        runtime_configuration: Optional[dict] = None,
    ):
        dependencies: dict = super()._get_evaluation_dependencies(
            metric=metric,
            configuration=configuration,
            execution_engine=execution_engine,
            runtime_configuration=runtime_configuration,
        )

        if (
            metric.metric_name
            == f"column_values.value_length.equals.{MetricPartialFunctionTypeSuffixes.CONDITION.value}"  # noqa: E501 # FIXME CoP
        ):
            dependencies[
                f"column_values.value_length.{MetricPartialFunctionTypeSuffixes.MAP.value}"
            ] = MetricConfiguration(
                metric_name=f"column_values.value_length.{MetricPartialFunctionTypeSuffixes.MAP.value}",
                metric_domain_kwargs=metric.metric_domain_kwargs,
            )

        return dependencies


class ColumnValuesValueLength(ColumnMapMetricProvider):
    condition_metric_name = "column_values.value_length.between"
    function_metric_name = "column_values.value_length"

    condition_value_keys = (
        "min_value",
        "max_value",
        "strict_min",
        "strict_max",
    )

    @column_function_partial(engine=PandasExecutionEngine)
    def _pandas_function(cls, column, **kwargs):
        return column.astype(str).str.len()

    @column_function_partial(engine=SqlAlchemyExecutionEngine)
    def _sqlalchemy_function(cls, column, **kwargs):
        return sa.func.length(column)

    @column_function_partial(engine=SparkDFExecutionEngine)
    def _spark_function(cls, column, **kwargs):
        return F.length(column)

    @column_condition_partial(engine=PandasExecutionEngine)
    def _pandas(  # noqa: C901 # FIXME CoP
        cls,
        column,
        _metrics,
        min_value=None,
        max_value=None,
        strict_min=None,
        strict_max=None,
        **kwargs,
    ):
        column_lengths, _, _ = _metrics.get(
            f"column_values.value_length.{MetricPartialFunctionTypeSuffixes.MAP.value}"
        )

        metric_series = None
        if min_value is not None and max_value is not None:
            # the word "strict" can be taken as a synonym for the word "exclusive"
            if strict_min and strict_max:
                metric_series = pandas_series_between(
                    series=column_lengths,
                    min_value=min_value,
                    max_value=max_value,
                    inclusive="neither",
                )
            elif strict_min and not strict_max:
                metric_series = pandas_series_between(
                    series=column_lengths,
                    min_value=min_value,
                    max_value=max_value,
                    inclusive="right",
                )
            elif not strict_min and strict_max:
                metric_series = pandas_series_between(
                    series=column_lengths,
                    min_value=min_value,
                    max_value=max_value,
                    inclusive="left",
                )
            elif not strict_min and not strict_max:
                metric_series = pandas_series_between(
                    series=column_lengths,
                    min_value=min_value,
                    max_value=max_value,
                    inclusive="both",
                )
        elif min_value is None and max_value is not None:
            if strict_max:
                metric_series = column_lengths < max_value
            else:
                metric_series = column_lengths <= max_value
        elif min_value is not None and max_value is None:
            if strict_min:
                metric_series = column_lengths > min_value
            else:
                metric_series = column_lengths >= min_value

        else:
            raise ValueError("Invalid configuration")  # noqa: TRY003 # FIXME CoP

        return metric_series

    @column_condition_partial(engine=SqlAlchemyExecutionEngine)
    def _sqlalchemy(
        cls,
        column,
        _metrics,
        min_value=None,
        max_value=None,
        strict_min=None,
        strict_max=None,
        **kwargs,
    ):
        column_lengths, _, _ = _metrics.get(
            f"column_values.value_length.{MetricPartialFunctionTypeSuffixes.MAP.value}"
        )

        if min_value is None and max_value is None:
            raise ValueError("min_value and max_value cannot both be None")  # noqa: TRY003 # FIXME CoP

        # Assert that min_value and max_value are integers
        try:
            if min_value is not None and not float(min_value).is_integer():
                raise ValueError("min_value and max_value must be integers")  # noqa: TRY003, TRY301 # FIXME CoP

            if max_value is not None and not float(max_value).is_integer():
                raise ValueError("min_value and max_value must be integers")  # noqa: TRY003, TRY301 # FIXME CoP

        except ValueError:
            raise ValueError("min_value and max_value must be integers")  # noqa: TRY003 # FIXME CoP

        if min_value is not None and max_value is not None:
            return sa.and_(
                column_lengths >= min_value,
                column_lengths <= max_value,
            )

        elif min_value is None and max_value is not None:
            return column_lengths <= max_value

        elif min_value is not None and max_value is None:
            return column_lengths >= min_value

    @column_condition_partial(engine=SparkDFExecutionEngine)
    def _spark(
        cls,
        column,
        _metrics,
        min_value=None,
        max_value=None,
        strict_min=None,
        strict_max=None,
        **kwargs,
    ):
        column_lengths, _, _ = _metrics.get(
            f"column_values.value_length.{MetricPartialFunctionTypeSuffixes.MAP.value}"
        )

        if min_value is None and max_value is None:
            raise ValueError("min_value and max_value cannot both be None")  # noqa: TRY003 # FIXME CoP

        # Assert that min_value and max_value are integers
        try:
            if min_value is not None and not float(min_value).is_integer():
                raise ValueError("min_value and max_value must be integers")  # noqa: TRY003, TRY301 # FIXME CoP

            if max_value is not None and not float(max_value).is_integer():
                raise ValueError("min_value and max_value must be integers")  # noqa: TRY003, TRY301 # FIXME CoP

        except ValueError:
            raise ValueError("min_value and max_value must be integers")  # noqa: TRY003 # FIXME CoP

        if min_value is not None and max_value is not None:
            return (column_lengths >= min_value) & (column_lengths <= max_value)

        elif min_value is None and max_value is not None:
            return column_lengths <= max_value

        elif min_value is not None and max_value is None:
            return column_lengths >= min_value

    @classmethod
    @override
    def _get_evaluation_dependencies(
        cls,
        metric: MetricConfiguration,
        configuration: Optional[ExpectationConfiguration] = None,
        execution_engine: Optional[ExecutionEngine] = None,
        runtime_configuration: Optional[dict] = None,
    ):
        dependencies: dict = super()._get_evaluation_dependencies(
            metric=metric,
            configuration=configuration,
            execution_engine=execution_engine,
            runtime_configuration=runtime_configuration,
        )

        if (
            metric.metric_name
            == f"column_values.value_length.between.{MetricPartialFunctionTypeSuffixes.CONDITION.value}"  # noqa: E501 # FIXME CoP
        ):
            dependencies[
                f"column_values.value_length.{MetricPartialFunctionTypeSuffixes.MAP.value}"
            ] = MetricConfiguration(
                metric_name=f"column_values.value_length.{MetricPartialFunctionTypeSuffixes.MAP.value}",
                metric_domain_kwargs=metric.metric_domain_kwargs,
            )

        return dependencies
