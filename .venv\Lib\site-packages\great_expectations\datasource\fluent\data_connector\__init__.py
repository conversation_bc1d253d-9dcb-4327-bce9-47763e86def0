# isort:skip_file

from great_expectations.datasource.fluent.data_connector.data_connector import (
    DataConnector,
)
from great_expectations.datasource.fluent.data_connector.file_path_data_connector import (
    FilePathDataConnector,
)
from great_expectations.datasource.fluent.data_connector.filesystem_data_connector import (
    FilesystemDataConnector,
)
from great_expectations.datasource.fluent.data_connector.dbfs_data_connector import (
    DBFSDataConnector,
)
from great_expectations.datasource.fluent.data_connector.s3_data_connector import (
    S3DataConnector,
)
from great_expectations.datasource.fluent.data_connector.azure_blob_storage_data_connector import (
    AzureBlobStorageDataConnector,
)
from great_expectations.datasource.fluent.data_connector.google_cloud_storage_data_connector import (  # noqa: E501 # FIXME CoP
    GoogleCloudStorageDataConnector,
)

FILE_PATH_BATCH_SPEC_KEY = FilePathDataConnector.FILE_PATH_BATCH_SPEC_KEY
