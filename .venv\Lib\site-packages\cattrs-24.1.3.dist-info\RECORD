cattr/__init__.py,sha256=pODFKaZ7MisyHe_XPc9X6KKG73mqduHUvQO142XwijY,906
cattr/__pycache__/__init__.cpython-312.pyc,,
cattr/__pycache__/converters.cpython-312.pyc,,
cattr/__pycache__/disambiguators.cpython-312.pyc,,
cattr/__pycache__/dispatch.cpython-312.pyc,,
cattr/__pycache__/errors.cpython-312.pyc,,
cattr/__pycache__/gen.cpython-312.pyc,,
cattr/converters.py,sha256=rQhY4J8r7QTZh5WICuFe4GWO1v0DS3DgQ9r569zd6jg,192
cattr/disambiguators.py,sha256=ugD1fq1Z5x1pGu5P1lMzcT-IEi1q7IfQJIHEdmg62vM,103
cattr/dispatch.py,sha256=uVEOgHWR9Hn5tm-wIw-bDccqrxJByVi8yRKaYyvL67k,125
cattr/errors.py,sha256=V4RhoCObwGrlaM3oyn1H_FYxGR8iAB9dG5NxFDYM548,343
cattr/gen.py,sha256=hWyKoZ_d2D36Jz_npspyGw8s9pWtUA69sXf0R3uOvgM,597
cattr/preconf/__init__.py,sha256=NqPE7uhVfcP-PggkUpsbfAutMo8oHjcoB1cvjgLft-s,78
cattr/preconf/__pycache__/__init__.cpython-312.pyc,,
cattr/preconf/__pycache__/bson.cpython-312.pyc,,
cattr/preconf/__pycache__/json.cpython-312.pyc,,
cattr/preconf/__pycache__/msgpack.cpython-312.pyc,,
cattr/preconf/__pycache__/orjson.cpython-312.pyc,,
cattr/preconf/__pycache__/pyyaml.cpython-312.pyc,,
cattr/preconf/__pycache__/tomlkit.cpython-312.pyc,,
cattr/preconf/__pycache__/ujson.cpython-312.pyc,,
cattr/preconf/bson.py,sha256=Bn4hJxac7OthGg_CR4LCPeBp_fz4kx3QniBVOZhguGs,195
cattr/preconf/json.py,sha256=HBxWOTqKI7HOlmt-GnN6_wjQz1VphRi70sAOEbx0A2Y,206
cattr/preconf/msgpack.py,sha256=VXqynPel11_lX8uTg84-u27LQhCqL1OoiF-lTqnoAkQ,207
cattr/preconf/orjson.py,sha256=fs8qDPDYSBba9D8ib9Df1WVZ8iZaRPQq7kDigAxp14E,203
cattr/preconf/pyyaml.py,sha256=lhuKwHrcvr16WOtdW4Q0mgIRzB90v1hwZkFXtPKOvAw,203
cattr/preconf/tomlkit.py,sha256=rk393txIBHeWR66LfnATPh9Im1EFAHPJvSEGGSP2c-8,207
cattr/preconf/ujson.py,sha256=r6ufraKDqmKdetNZUKxLYVSGmuJ-ckc-UjGYvCamr9k,199
cattr/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cattrs-24.1.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
cattrs-24.1.3.dist-info/METADATA,sha256=BeF2f24ebE23Ssz1xE9LN89QPhiPmTrXb2UNk_cOLOI,8420
cattrs-24.1.3.dist-info/RECORD,,
cattrs-24.1.3.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
cattrs-24.1.3.dist-info/licenses/LICENSE,sha256=9fudHt43qIykf0IMSZ3KD0oFvJk-Esd9I1IKrSkcAb8,1074
cattrs/__init__.py,sha256=peO0_Q9AEguPCMjXlRH-Nj0CahcCw5CJmpnpKxsWKSQ,1835
cattrs/__pycache__/__init__.cpython-312.pyc,,
cattrs/__pycache__/_compat.cpython-312.pyc,,
cattrs/__pycache__/_generics.cpython-312.pyc,,
cattrs/__pycache__/cols.cpython-312.pyc,,
cattrs/__pycache__/converters.cpython-312.pyc,,
cattrs/__pycache__/disambiguators.cpython-312.pyc,,
cattrs/__pycache__/dispatch.cpython-312.pyc,,
cattrs/__pycache__/errors.cpython-312.pyc,,
cattrs/__pycache__/fns.cpython-312.pyc,,
cattrs/__pycache__/v.cpython-312.pyc,,
cattrs/_compat.py,sha256=NiNN3F9hDI5Cbl14BSdrQcBYld-8YMyizDoubVgNs4U,17677
cattrs/_generics.py,sha256=ymyDdLjXoYi_XPBA_f_-xJC7Bc8RGqoUcdlwTbB7xl8,718
cattrs/cols.py,sha256=sB9NTOp8pGLMUxVicSHWpcX_4czrD1g5MdCJO0Ko5s0,8433
cattrs/converters.py,sha256=nMxuapDj3Q75oW4sVXnYdIeHhodwzLNUcDcaIfKMLQM,53916
cattrs/disambiguators.py,sha256=ljl73QtSB3MAGcl7-phAUR66b4yx_1ORYLb5fUgW8bY,6825
cattrs/dispatch.py,sha256=fEE100tCqcqC_wl5y2FCdVEocLOuDlys0sduJrTfmB4,6810
cattrs/errors.py,sha256=rHps9Qp7SoRafb2VuAkMbhsQf4pq87gX1SzM-jluMsE,4070
cattrs/fns.py,sha256=xQceStzW4qLiMTJgGM-pVUudGwHm0Hin8oCYe1feS5c,633
cattrs/gen/__init__.py,sha256=yBOs4V1SQ6RAPFSGyIkwi4ZEU7fqA_nQrH6ujgT88eI,38527
cattrs/gen/__pycache__/__init__.cpython-312.pyc,,
cattrs/gen/__pycache__/_consts.cpython-312.pyc,,
cattrs/gen/__pycache__/_generics.cpython-312.pyc,,
cattrs/gen/__pycache__/_lc.cpython-312.pyc,,
cattrs/gen/__pycache__/_shared.cpython-312.pyc,,
cattrs/gen/__pycache__/typeddicts.cpython-312.pyc,,
cattrs/gen/_consts.py,sha256=ZwT_m2J3S7p-UjltpbA1WtfQZLNj9KhmFYCAv6Zl-g0,511
cattrs/gen/_generics.py,sha256=_DyXCGql2QIxGhAv3_B1hsi80uPK8PhK2hhZa95YOlo,3011
cattrs/gen/_lc.py,sha256=ktP5F9oOUo4HpZ4-hlLliLPzr8XjFi31EXMl8YMMs-g,906
cattrs/gen/_shared.py,sha256=4yX9-TD5yyVzDWlSjkECrQV5B82xHUeBt9n2N5UgOAE,2064
cattrs/gen/typeddicts.py,sha256=C3Bp8tNM-MI7L7KO0X3sfwSkG5d0ua3j7qDtvcCEBQk,22004
cattrs/preconf/__init__.py,sha256=dfkUXoU47ZJfmoKX9FsnARKqLlgJeBjMxORMzxrbKbs,604
cattrs/preconf/__pycache__/__init__.cpython-312.pyc,,
cattrs/preconf/__pycache__/bson.cpython-312.pyc,,
cattrs/preconf/__pycache__/cbor2.cpython-312.pyc,,
cattrs/preconf/__pycache__/json.cpython-312.pyc,,
cattrs/preconf/__pycache__/msgpack.cpython-312.pyc,,
cattrs/preconf/__pycache__/msgspec.cpython-312.pyc,,
cattrs/preconf/__pycache__/orjson.cpython-312.pyc,,
cattrs/preconf/__pycache__/pyyaml.cpython-312.pyc,,
cattrs/preconf/__pycache__/tomlkit.cpython-312.pyc,,
cattrs/preconf/__pycache__/ujson.cpython-312.pyc,,
cattrs/preconf/bson.py,sha256=uBRpTVfwGZ-qfuDYGwsl8eXokVAmcVBedKQPGUmamhc,3656
cattrs/preconf/cbor2.py,sha256=ANfQUXgs7pyU5-4_2hYmcqUxzQZhWhFzrk_0y6b1yYw,1635
cattrs/preconf/json.py,sha256=CSU5RosdYyg6cIOpaohgZVfdMtOtKjZlSg837fW4fTw,2035
cattrs/preconf/msgpack.py,sha256=cgwX_ARi_swQjG6hwa9j-n7FUynLNWIMVLouz_VoTuw,1753
cattrs/preconf/msgspec.py,sha256=f8J04RXv8UErKAwwzVs1cMbvoM-9erMmmF49zKBbCDo,6343
cattrs/preconf/orjson.py,sha256=RZ8DI-4K7Xi0QdpIihT9I3Cm-O8Aq8_MTt2R3a4fgEk,3241
cattrs/preconf/pyyaml.py,sha256=Ga96zLypn2DglTgbrb9h3jcuH-caur_UQI1ADo-ynUA,2298
cattrs/preconf/tomlkit.py,sha256=2k-BN0ZW3faWmHcMQ1bCvsKCClhdgSjTe056O1xEc4o,3060
cattrs/preconf/ujson.py,sha256=JBh5dWluwMwKhAJPINJhpse_aQ1p9hzrGo8BuvmG6S0,1863
cattrs/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cattrs/strategies/__init__.py,sha256=nkZWCzSRYcS-75FMfk52mioZSuWykaN8hB39Vig5Xkg,339
cattrs/strategies/__pycache__/__init__.cpython-312.pyc,,
cattrs/strategies/__pycache__/_class_methods.cpython-312.pyc,,
cattrs/strategies/__pycache__/_subclasses.cpython-312.pyc,,
cattrs/strategies/__pycache__/_unions.cpython-312.pyc,,
cattrs/strategies/_class_methods.py,sha256=vfiE3wKm04oc-3T9hchsIyhVzpMpJRdgTbujKsWyVpQ,2597
cattrs/strategies/_subclasses.py,sha256=zzhLl7fSZlmlBuBY-rPX7L1d_C5tiDFDBmUTeRpG2uI,9204
cattrs/strategies/_unions.py,sha256=l8CjVVFAwftkBa47g3m2KgtQ_b42Wnv-KwYY_LHReCA,9166
cattrs/v.py,sha256=cTYt0EW8yr-gzKynw4_XjFv3RLpAF8IebvOb612l9QE,4399
